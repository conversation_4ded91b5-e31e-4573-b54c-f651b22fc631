# 光电目标数据产生规范

## 概述

本文档详细描述了光电对抗仿真系统中光电目标设备的数据产生规范，包括输入配置要求和输出数据结构。光电目标设备是仿真系统的核心组件，负责模拟各种光电探测和跟踪设备的行为。

## API接口信息

- **接口地址**: `POST /run_simulation`
- **Content-Type**: `application/json`
- **响应格式**: JSON
- **服务端口**: 8587

## 光电目标输入配置规范

### optical_targets字段结构

光电目标配置为数组类型，每个数组元素包含以下字段：

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| model | string | 设备型号名称 | - | - | - | "多目标_红外目标A" | 必需字段 |
| position | object | 设备位置信息 | - | - | - | - | 必需字段 |
| observation_direction | object | 观察方向 | - | - | - | - | 必需字段 |
| performance_params | object | 性能参数 | - | - | - | - | 必需字段 |
| work_mode | string | 工作模式 | - | - | passive_search, active_tracking, coordinated_search, active_illumination, passive_observation | "coordinated_search" | 必需字段 |
| temperature | object | 温度参数 | - | - | - | - | 红外目标专用，可选字段 |

### position字段详细规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| latitude | number | 纬度 | 度 | -90到90 | - | 39.9042 | WGS84坐标系 |
| longitude | number | 经度 | 度 | -180到180 | - | 116.4074 | WGS84坐标系 |
| altitude | number | 海拔高度 | m | ≥0 | - | 1500.0 | 海平面以上高度 |

### observation_direction字段详细规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| azimuth | number | 方位角 | 度 | 0-360 | - | 45.0 | 正北为0度，顺时针 |
| elevation | number | 俯仰角 | 度 | -90到90 | - | 10.0 | 水平为0度，向上为正 |

### performance_params字段详细规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| detection_range | number | 探测距离 | m | >0 | - | 20000 | 最大探测距离 |
| resolution | number | 分辨率 | mrad | >0 | - | 0.06 | 角分辨率 |
| field_of_view | number | 视场角 | 度 | >0 | - | 15.0 | 观察视场范围 |
| wavelength | number | 工作波长 | m | >0 | - | 1.064e-6 | 激光设备专用 |
| power | number | 功率 | W | >0 | - | 1800 | 激光设备专用 |
| spectral_range | array | 光谱范围 | m | [min, max] | - | [0.4e-6, 0.7e-6] | 电视设备专用 |
| sensitivity | number | 灵敏度 | - | 0-1 | - | 0.9 | 可选字段 |

### temperature字段详细规范（红外目标专用）

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| engine | number | 发动机温度 | K | >0 | - | 520.0 | 高温部件 |
| body | number | 机体温度 | K | >0 | - | 305.0 | 主体温度 |
| background | number | 背景温度 | K | >0 | - | 291.15 | 环境背景温度 |

## 光电目标输出数据规范

### 输出文件类型

光电目标设备会产生以下类型的输出文件：

| 文件类型 | 说明 | 文件格式 | 数量 | 备注 |
|----------|------|----------|------|------|
| 静态图像 | 目标探测图像 | PNG | data_count个 | 当output_types包含static_images时 |
| 动态视频 | 目标跟踪视频 | MP4 | 1个 | 当output_types包含dynamic_images时 |
| 参数数据 | 性能参数数据 | JSON | 1个 | 当output_types包含parameters时 |

### simulation_data.targets数组元素结构

每个光电目标设备在simulation_data.targets数组中对应一个元素，包含以下字段：

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| device_info | object | 设备信息 | - | - | - | - | 设备基本信息 |
| parameter_data | object | 参数数据 | - | - | - | - | 性能参数数据 |
| statistics | object | 统计信息 | - | - | - | - | 数据生成统计 |

### device_info字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| device_id | integer | 设备标识符 | - | ≥0 | - | 0 | 设备在数组中的索引 |
| model | string | 设备型号 | - | - | - | "多目标_红外目标A" | 输入配置的回显 |
| device_type | string | 设备类型 | - | - | optical_target | "optical_target" | 固定值 |
| generation_timestamp | string | 数据生成时间戳 | - | - | - | "2025-08-15T22:52:02.277641" | ISO 8601格式 |

### parameter_data字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| deviation_range | array | 偏离范围数据 | - | - | - | - | 角度偏离测量数据 |
| recognition_accuracy | array | 识别准确率数据 | - | - | - | - | 目标识别性能数据 |
| detection_range | array | 探测距离数据 | - | - | - | - | 探测性能数据 |
| detection_probability | array | 探测概率数据 | - | - | - | - | 探测概率统计数据 |

### deviation_range数组元素字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| sample_id | integer | 样本标识符 | - | ≥0 | - | 0 | 样本序号 |
| azimuth_deviation_mrad | number | 方位角偏离 | mrad | ≥0 | - | 47.066 | 方位角偏离量 |
| elevation_deviation_mrad | number | 俯仰角偏离 | mrad | ≥0 | - | 77.284 | 俯仰角偏离量 |
| total_deviation_mrad | number | 总偏离量 | mrad | ≥0 | - | 90.488 | 总体偏离量 |
| weather_condition | string | 天气条件 | - | - | - | "clear_weather" | 测量时天气 |
| timestamp | string | 时间戳 | - | - | - | "2025-08-15T22:52:02.277641" | ISO 8601格式 |

### recognition_accuracy数组元素字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| sample_id | integer | 样本标识符 | - | ≥0 | - | 0 | 样本序号 |
| recognition_accuracy | number | 识别准确率 | - | 0-1 | - | 0.499 | 目标识别准确率 |
| distance_m | number | 目标距离 | m | >0 | - | 15076.5 | 目标到传感器距离 |
| weather_factor | number | 天气影响因子 | - | 0-1 | - | 0.913 | 天气对识别的影响 |
| distance_factor | number | 距离影响因子 | - | 0-1 | - | 0.704 | 距离对识别的影响 |
| timestamp | string | 时间戳 | - | - | - | "2025-08-15T22:52:02.277641" | ISO 8601格式 |

### detection_range数组元素字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| sample_id | integer | 样本标识符 | - | ≥0 | - | 0 | 样本序号 |
| detection_range_m | number | 实际探测距离 | m | >0 | - | 20110.9 | 实际探测距离 |
| base_range_m | number | 基础探测距离 | m | >0 | - | 20000 | 理论探测距离 |
| weather_factor | number | 天气影响因子 | - | 0-1 | - | 0.9 | 天气对探测的影响 |
| target_contrast | number | 目标对比度 | - | >0 | - | 1.117 | 目标与背景对比度 |
| weather_condition | string | 天气条件 | - | - | - | "clear_weather" | 测量时天气 |
| timestamp | string | 时间戳 | - | - | - | "2025-08-15T22:52:02.277641" | ISO 8601格式 |

### detection_probability数组元素字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| sample_id | integer | 样本标识符 | - | ≥0 | - | 0 | 样本序号 |
| detection_probability | number | 探测概率 | - | 0-1 | - | 0.724 | 目标被探测到的概率 |
| distance_m | number | 目标距离 | m | >0 | - | 5017.6 | 目标到传感器距离 |
| base_probability | number | 基础探测概率 | - | 0-1 | - | 0.95 | 理论探测概率 |
| weather_factor | number | 天气影响因子 | - | 0-1 | - | 0.928 | 天气对探测的影响 |
| noise_factor | number | 噪声影响因子 | - | 0-1 | - | 0.821 | 噪声对探测的影响 |
| timestamp | string | 时间戳 | - | - | - | "2025-08-15T22:52:02.277641" | ISO 8601格式 |

### statistics字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| total_samples | integer | 总样本数 | 个 | ≥0 | - | 3 | 生成的样本总数 |
| data_categories | integer | 数据类别数 | 个 | ≥0 | - | 4 | 数据类别总数 |
| sample_count_per_category | object | 各类别样本数 | - | - | - | - | 各类别的样本统计 |

## 光电目标配置示例

### 红外目标配置示例

```json
{
  "model": "多目标_红外目标A",
  "position": {
    "latitude": 39.9042,
    "longitude": 116.4074,
    "altitude": 1500.0
  },
  "observation_direction": {
    "azimuth": 45.0,
    "elevation": 10.0
  },
  "performance_params": {
    "detection_range": 20000,
    "resolution": 0.06,
    "field_of_view": 15.0
  },
  "work_mode": "coordinated_search",
  "temperature": {
    "engine": 520.0,
    "body": 305.0,
    "background": 291.15
  }
}
```

### 激光目标配置示例

```json
{
  "model": "多目标_激光目标C",
  "position": {
    "latitude": 39.9000,
    "longitude": 116.4200,
    "altitude": 1800.0
  },
  "observation_direction": {
    "azimuth": 135.0,
    "elevation": 15.0
  },
  "performance_params": {
    "detection_range": 25000,
    "resolution": 0.03,
    "field_of_view": 8.0,
    "wavelength": 1.064e-6,
    "power": 1800
  },
  "work_mode": "active_illumination"
}
```

### 电视目标配置示例

```json
{
  "model": "多目标_电视目标D",
  "position": {
    "latitude": 39.8950,
    "longitude": 116.4100,
    "altitude": 1000.0
  },
  "observation_direction": {
    "azimuth": 180.0,
    "elevation": 8.0
  },
  "performance_params": {
    "detection_range": 15000,
    "resolution": 0.12,
    "field_of_view": 20.0,
    "spectral_range": [0.4e-6, 0.7e-6]
  },
  "work_mode": "passive_observation"
}
```

## 光电目标输出数据示例

```json
{
  "device_info": {
    "device_id": 0,
    "model": "多目标_红外目标A",
    "device_type": "optical_target",
    "generation_timestamp": "2025-08-15T22:52:02.277641"
  },
  "parameter_data": {
    "deviation_range": [
      {
        "sample_id": 0,
        "azimuth_deviation_mrad": 47.066,
        "elevation_deviation_mrad": 77.284,
        "total_deviation_mrad": 90.488,
        "weather_condition": "clear_weather",
        "timestamp": "2025-08-15T22:52:02.277641"
      },
      {
        "sample_id": 1,
        "azimuth_deviation_mrad": 59.21,
        "elevation_deviation_mrad": 32.91,
        "total_deviation_mrad": 67.742,
        "weather_condition": "clear_weather",
        "timestamp": "2025-08-15T22:52:02.277641"
      }
    ],
    "recognition_accuracy": [
      {
        "sample_id": 0,
        "recognition_accuracy": 0.499,
        "distance_m": 15076.5,
        "weather_factor": 0.913,
        "distance_factor": 0.704,
        "timestamp": "2025-08-15T22:52:02.277641"
      },
      {
        "sample_id": 1,
        "recognition_accuracy": 0.582,
        "distance_m": 7546.4,
        "weather_factor": 0.938,
        "distance_factor": 0.862,
        "timestamp": "2025-08-15T22:52:02.277641"
      }
    ],
    "detection_range": [
      {
        "sample_id": 0,
        "detection_range_m": 20110.9,
        "base_range_m": 20000,
        "weather_factor": 0.9,
        "target_contrast": 1.117,
        "weather_condition": "clear_weather",
        "timestamp": "2025-08-15T22:52:02.277641"
      }
    ],
    "detection_probability": [
      {
        "sample_id": 0,
        "detection_probability": 0.724,
        "distance_m": 5017.6,
        "base_probability": 0.95,
        "weather_factor": 0.928,
        "noise_factor": 0.821,
        "timestamp": "2025-08-15T22:52:02.277641"
      }
    ]
  },
  "statistics": {
    "total_samples": 3,
    "data_categories": 4,
    "sample_count_per_category": {
      "deviation_range": 3,
      "recognition_accuracy": 3,
      "detection_range": 3,
      "detection_probability": 3
    }
  }
}
```

## 数据验证规则

### 输入数据验证

1. **必需字段验证**：
   - `model`、`position`、`observation_direction`、`performance_params`、`work_mode`为必需字段
   - `position`必须包含`latitude`、`longitude`、`altitude`
   - `observation_direction`必须包含`azimuth`、`elevation`
   - `performance_params`必须包含`detection_range`、`resolution`、`field_of_view`

2. **数值范围验证**：
   - `latitude`：-90到90度
   - `longitude`：-180到180度
   - `altitude`：必须大于等于0
   - `azimuth`：0-360度
   - `elevation`：-90到90度
   - `detection_range`、`resolution`、`field_of_view`：必须大于0

3. **工作模式验证**：
   - 有效值：`passive_search`、`active_tracking`、`coordinated_search`、`active_illumination`、`passive_observation`

### 输出数据特点

1. **数据完整性**：每个目标设备都会生成4类性能数据，每类包含data_count个样本
2. **时间戳一致性**：同一设备的所有数据使用相同的生成时间戳
3. **物理模型准确性**：所有数值都基于物理模型计算，考虑环境因素影响
4. **统计信息完整**：提供详细的数据生成统计信息

## 注意事项

1. **设备类型识别**：
   - 红外目标：需要配置temperature字段
   - 激光目标：需要配置wavelength和power参数
   - 电视目标：需要配置spectral_range参数

2. **性能影响因素**：
   - 天气条件影响所有性能指标
   - 距离对识别准确率和探测概率有显著影响
   - 目标对比度影响探测距离

3. **数据生成规律**：
   - 偏离范围基于设备分辨率计算
   - 识别准确率随距离增加而降低
   - 探测距离受天气和目标特性影响
   - 探测概率综合考虑多种噪声因素
