# 光电对抗仿真系统HTTP API输入输出数据规范

## 概述

本文档详细描述了光电对抗仿真系统HTTP API的输入和输出数据规范。系统基于FastAPI框架，提供POST `/run_simulation`接口执行光电对抗仿真，支持多种光电设备的建模和仿真。

## API接口信息

- **接口地址**: `POST /run_simulation`
- **Content-Type**: `application/json`
- **响应格式**: JSON
- **服务端口**: 8587
- **API文档**: `http://localhost:8587/docs`

## 输入数据规范

### 请求体结构

输入数据为JSON格式，包含以下顶级字段：

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| simulation | object | 仿真配置参数 | - | - | - | - | 必需字段 |
| system | object | 系统配置参数 | - | - | - | - | 必需字段 |
| optical_targets | array | 光电目标设备列表 | - | - | - | - | 必需字段，至少一个设备 |
| optical_jammers | array | 光电干扰设备列表 | - | - | - | - | 可选字段 |
| optical_recons | array | 光电侦察设备列表 | - | - | - | - | 可选字段 |
| output_base_dir | string | 输出基础目录路径 | - | - | - | "./simulation_results" | 可选字段 |
| log_level | string | 日志级别 | - | - | DEBUG, INFO, WARNING, ERROR | "INFO" | 可选字段 |
| num_threads | integer | 并行线程数 | 个 | 1-32 | - | 4 | 可选字段，自动检测 |

### simulation字段详细规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| scenario_name | string | 仿真场景名称 | - | - | - | "多目标光电对抗场景" | 必需字段 |
| duration | number | 仿真时长 | 秒 | >0 | - | 10.0 | 必需字段 |
| time_step | number | 时间步长 | 秒 | >0 | - | 0.1 | 必需字段 |
| data_count | integer | 数据生成数量 | 个 | 1-5000 | - | 3 | 必需字段，限制最大5000 |
| output_types | array | 输出类型列表 | - | - | static_images, dynamic_images, parameters | ["static_images", "dynamic_images", "parameters"] | 必需字段 |
| environment | object | 环境参数配置 | - | - | - | - | 必需字段 |

### simulation.environment字段详细规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| weather_condition | string | 天气条件 | - | - | clear_weather, fog, rain, snow | "clear_weather" | 影响大气传输 |
| temperature | number | 环境温度 | K | >0 | - | 291.15 | 影响红外辐射 |
| humidity | number | 相对湿度 | - | 0-1 | - | 0.6 | 影响大气吸收 |
| pressure | number | 大气压强 | Pa | >0 | - | 101325 | 标准大气压 |
| wind_speed | number | 风速 | m/s | ≥0 | - | 5.0 | 影响烟幕扩散 |
| visibility | number | 能见度 | m | >0 | - | 25000 | 影响光学传输 |
| cloud_cover | number | 云量覆盖 | - | 0-1 | - | 0.0 | 可选字段 |
| precipitation | number | 降水量 | mm/h | ≥0 | - | 0.0 | 可选字段 |
| atmospheric_turbulence | number | 大气湍流强度 | - | 0-1 | - | 0.2 | 可选字段 |

### system字段详细规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| max_threads | integer | 最大线程数 | 个 | 1-32 | - | 8 | 影响并行处理性能 |
| image_resolution | array | 图像分辨率 | 像素 | [width, height] | - | [640, 480] | 建议640x480 |
| video_fps | integer | 视频帧率 | fps | 1-60 | - | 30 | 动态图像帧率 |
| random_seed | integer | 随机种子 | - | - | - | 1234 | 可选字段，用于结果重现 |

### optical_targets字段详细规范（数组元素）

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| model | string | 设备型号名称 | - | - | - | "多目标_红外目标A" | 必需字段 |
| position | object | 设备位置信息 | - | - | - | - | 必需字段 |
| position.latitude | number | 纬度 | 度 | -90到90 | - | 39.9042 | WGS84坐标系 |
| position.longitude | number | 经度 | 度 | -180到180 | - | 116.4074 | WGS84坐标系 |
| position.altitude | number | 海拔高度 | m | ≥0 | - | 1500.0 | 海平面以上高度 |
| observation_direction | object | 观察方向 | - | - | - | - | 必需字段 |
| observation_direction.azimuth | number | 方位角 | 度 | 0-360 | - | 45.0 | 正北为0度，顺时针 |
| observation_direction.elevation | number | 俯仰角 | 度 | -90到90 | - | 10.0 | 水平为0度，向上为正 |
| performance_params | object | 性能参数 | - | - | - | - | 必需字段 |
| work_mode | string | 工作模式 | - | - | passive_search, active_tracking, coordinated_search | "coordinated_search" | 必需字段 |

### optical_targets.performance_params字段详细规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| detection_range | number | 探测距离 | m | >0 | - | 20000 | 最大探测距离 |
| resolution | number | 分辨率 | mrad | >0 | - | 0.06 | 角分辨率 |
| field_of_view | number | 视场角 | 度 | >0 | - | 15.0 | 观察视场范围 |
| wavelength | number | 工作波长 | m | >0 | - | 1.064e-6 | 激光设备专用 |
| power | number | 功率 | W | >0 | - | 1800 | 激光设备专用 |
| spectral_range | array | 光谱范围 | m | [min, max] | - | [0.4e-6, 0.7e-6] | 电视设备专用 |
| sensitivity | number | 灵敏度 | - | 0-1 | - | 0.9 | 可选字段 |

### optical_targets.temperature字段详细规范（红外目标专用）

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| engine | number | 发动机温度 | K | >0 | - | 520.0 | 高温部件 |
| body | number | 机体温度 | K | >0 | - | 305.0 | 主体温度 |
| background | number | 背景温度 | K | >0 | - | 291.15 | 环境背景温度 |

### optical_jammers字段详细规范（数组元素）

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| model | string | 设备型号名称 | - | - | - | "多目标_烟幕干扰1" | 必需字段 |
| position | object | 设备位置信息 | - | - | - | - | 必需字段，格式同targets |
| jamming_direction | object | 干扰方向 | - | - | - | - | 必需字段 |
| jamming_direction.azimuth | number | 方位角 | 度 | 0-360 | - | 60.0 | 干扰指向方位 |
| jamming_direction.elevation | number | 俯仰角 | 度 | -90到90 | - | 0.0 | 干扰指向俯仰 |
| performance_params | object | 性能参数 | - | - | - | - | 必需字段 |
| work_mode | string | 工作模式 | - | - | area_denial, active_dazzling, continuous | "area_denial" | 必需字段 |
| jamming_strategy | string | 干扰策略 | - | - | coordinated_obscuration, sensor_overload, broadband | "coordinated_obscuration" | 必需字段 |

### optical_jammers.performance_params字段详细规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| coverage_range | number | 覆盖距离 | m | >0 | - | 4000 | 烟幕干扰专用 |
| duration | number | 持续时间 | s | >0 | - | 300 | 烟幕干扰专用 |
| coverage_radius | number | 覆盖半径 | m | >0 | - | 150 | 烟幕干扰专用 |
| jamming_power | number | 干扰功率 | W | >0 | - | 3000 | 激光干扰专用 |
| jamming_frequency | number | 干扰频率 | Hz | >0 | - | 1200 | 激光干扰专用 |
| wavelength | number | 工作波长 | m | >0 | - | 0.532e-6 | 激光干扰专用 |
| beam_width | number | 光束宽度 | 度 | >0 | - | 2.0 | 可选字段 |

### optical_recons字段详细规范（数组元素）

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| model | string | 设备型号名称 | - | - | - | "多目标_侦察系统1" | 必需字段 |
| position | object | 设备位置信息 | - | - | - | - | 必需字段，格式同targets |
| performance_params | object | 性能参数 | - | - | - | - | 必需字段 |
| work_mode | string | 工作模式 | - | - | multi_target_tracking, coordinated_surveillance, passive_detection | "multi_target_tracking" | 必需字段 |
| detection_mode | string | 检测模式 | - | - | infrared_warning, laser_warning, electro_optical | "infrared_warning" | 必需字段 |

### optical_recons.performance_params字段详细规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| detection_range | number | 探测距离 | m | >0 | - | 35000 | 最大探测距离 |
| resolution | number | 分辨率 | mrad | >0 | - | 0.02 | 角分辨率 |
| spectral_coverage | array | 光谱覆盖范围 | m | [min, max] | - | [3e-6, 12e-6] | 工作波段 |
| sensitivity | number | 灵敏度 | - | 0-1 | - | 0.96 | 探测灵敏度 |
| field_of_view | number | 视场角 | 度 | >0 | - | 30.0 | 观察视场范围 |
| multi_target_capacity | integer | 多目标容量 | 个 | >0 | - | 15 | 同时跟踪目标数 |

## 输出数据规范

### 响应体结构

输出数据为JSON格式，包含以下顶级字段：

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| success | boolean | 仿真执行状态 | - | - | true, false | true | 必返回字段 |
| session_info | object | 会话信息 | - | - | - | - | 必返回字段 |
| simulation_config | object | 仿真配置摘要 | - | - | - | - | 成功时返回 |
| simulation_results | object | 仿真结果文件路径 | - | - | - | - | 成功时返回 |
| performance_metrics | object | 性能指标 | - | - | - | - | 成功时返回 |
| server_info | object | 服务器信息 | - | - | - | - | 成功时返回 |
| error_info | string | 错误信息 | - | - | - | null | 失败时返回 |

### session_info字段详细规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| start_time | string | 仿真开始时间 | - | - | - | "2025-08-15T22:51:40.844053" | ISO 8601格式 |
| end_time | string | 仿真结束时间 | - | - | - | "2025-08-15T22:52:02.333639" | ISO 8601格式 |
| duration | number | 执行时长 | 秒 | ≥0 | - | 21.489586 | 实际执行时间 |
| session_id | string | 会话标识符 | - | - | - | "session_20250815_225140_844" | 唯一标识 |
| output_directory | string | 输出目录路径 | - | - | - | "simulation_results/session_20250815_225140_844" | URL编码后的路径 |

### simulation_config字段详细规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| scenario_name | string | 场景名称 | - | - | - | "多目标光电对抗场景" | 输入配置的回显 |
| duration | number | 仿真时长 | 秒 | >0 | - | 10.0 | 输入配置的回显 |
| time_step | number | 时间步长 | 秒 | >0 | - | 0.1 | 输入配置的回显 |
| data_count | integer | 数据生成数量 | 个 | 1-5000 | - | 3 | 输入配置的回显 |
| output_types | array | 输出类型列表 | - | - | - | ["static_images", "dynamic_images", "parameters"] | 输入配置的回显 |
| device_count | object | 设备数量统计 | - | - | - | - | 自动统计 |
| environment | object | 环境参数 | - | - | - | - | 输入配置的回显 |
| system_config | object | 系统配置 | - | - | - | - | 输入配置的回显 |

### simulation_config.device_count字段详细规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| optical_targets | integer | 光电目标数量 | 个 | ≥0 | - | 4 | 自动统计 |
| optical_jammers | integer | 光电干扰设备数量 | 个 | ≥0 | - | 2 | 自动统计 |
| optical_recons | integer | 光电侦察设备数量 | 个 | ≥0 | - | 2 | 自动统计 |

### simulation_results字段详细规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| images | array | 静态图像文件路径列表 | - | - | - | - | URL编码后的路径 |
| videos | array | 动态视频文件路径列表 | - | - | - | - | URL编码后的路径 |
| data | array | 数据文件路径列表 | - | - | - | - | URL编码后的路径 |
| summary | array | 摘要文件路径列表 | - | - | - | - | URL编码后的路径 |
| simulation_data | object | 仿真数据内容 | - | - | - | - | 详细数据内容 |

### simulation_results.simulation_data字段详细规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| targets | array | 光电目标仿真数据 | - | - | - | - | 目标设备数据数组 |
| jammers | array | 光电干扰仿真数据 | - | - | - | - | 干扰设备数据数组 |
| recons | array | 光电侦察仿真数据 | - | - | - | - | 侦察设备数据数组 |
| analysis | array | 综合分析数据 | - | - | - | - | 干扰效果分析等 |

### performance_metrics字段详细规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| processing_speed | number | 处理速度 | 数据/秒 | ≥0 | - | 0.14 | 数据生成速度 |
| files_per_second | number | 文件生成速度 | 文件/秒 | ≥0 | - | 1.12 | 文件生成速度 |
| data_entries_per_second | number | 数据条目生成速度 | 条目/秒 | ≥0 | - | 0.37 | 数据条目生成速度 |
| thread_count | integer | 使用线程数 | 个 | ≥1 | - | 8 | 实际使用线程数 |
| thread_utilization | number | 线程利用率 | - | 0-1 | - | 0.8 | 线程使用效率 |
| memory_usage | object | 内存使用情况 | - | - | - | - | 内存统计信息 |
| execution_efficiency | object | 执行效率统计 | - | - | - | - | 效率指标 |
| data_statistics | object | 数据统计信息 | - | - | - | - | 数据生成统计 |

### server_info字段详细规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| ipv4_address | string | 服务器IPv4地址 | - | - | - | "*************" | 本机IP地址 |
| port | integer | 服务端口 | - | 1-65535 | - | 8587 | HTTP服务端口 |
| base_url | string | 基础URL | - | - | - | "http://*************:8587" | 服务基础地址 |
| static_files_url | string | 静态文件URL | - | - | - | "http://*************:8587/simulation_results" | 文件下载地址 |
| session_list_url | string | 会话列表URL | - | - | - | "http://*************:8587/api/simulation_results" | 会话查询地址 |

## 详细仿真数据结构规范

### 光电目标仿真数据结构（targets数组元素）

#### device_info字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| device_id | integer | 设备标识符 | - | ≥0 | - | 0 | 设备在数组中的索引 |
| model | string | 设备型号 | - | - | - | "多目标_红外目标A" | 输入配置的回显 |
| device_type | string | 设备类型 | - | - | optical_target | "optical_target" | 固定值 |
| generation_timestamp | string | 数据生成时间戳 | - | - | - | "2025-08-15T22:52:02.277641" | ISO 8601格式 |

#### parameter_data字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| deviation_range | array | 偏离范围数据 | - | - | - | - | 角度偏离测量数据 |
| recognition_accuracy | array | 识别准确率数据 | - | - | - | - | 目标识别性能数据 |
| detection_range | array | 探测距离数据 | - | - | - | - | 探测性能数据 |
| detection_probability | array | 探测概率数据 | - | - | - | - | 探测概率统计数据 |

#### deviation_range数组元素字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| sample_id | integer | 样本标识符 | - | ≥0 | - | 0 | 样本序号 |
| azimuth_deviation_mrad | number | 方位角偏离 | mrad | ≥0 | - | 47.066 | 方位角偏离量 |
| elevation_deviation_mrad | number | 俯仰角偏离 | mrad | ≥0 | - | 77.284 | 俯仰角偏离量 |
| total_deviation_mrad | number | 总偏离量 | mrad | ≥0 | - | 90.488 | 总体偏离量 |
| weather_condition | string | 天气条件 | - | - | - | "clear_weather" | 测量时天气 |
| timestamp | string | 时间戳 | - | - | - | "2025-08-15T22:52:02.277641" | ISO 8601格式 |

#### recognition_accuracy数组元素字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| sample_id | integer | 样本标识符 | - | ≥0 | - | 0 | 样本序号 |
| recognition_accuracy | number | 识别准确率 | - | 0-1 | - | 0.499 | 目标识别准确率 |
| distance_m | number | 目标距离 | m | >0 | - | 15076.5 | 目标到传感器距离 |
| weather_factor | number | 天气影响因子 | - | 0-1 | - | 0.913 | 天气对识别的影响 |
| distance_factor | number | 距离影响因子 | - | 0-1 | - | 0.704 | 距离对识别的影响 |
| timestamp | string | 时间戳 | - | - | - | "2025-08-15T22:52:02.277641" | ISO 8601格式 |

#### detection_range数组元素字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| sample_id | integer | 样本标识符 | - | ≥0 | - | 0 | 样本序号 |
| detection_range_m | number | 实际探测距离 | m | >0 | - | 20110.9 | 实际探测距离 |
| base_range_m | number | 基础探测距离 | m | >0 | - | 20000 | 理论探测距离 |
| weather_factor | number | 天气影响因子 | - | 0-1 | - | 0.9 | 天气对探测的影响 |
| target_contrast | number | 目标对比度 | - | >0 | - | 1.117 | 目标与背景对比度 |
| weather_condition | string | 天气条件 | - | - | - | "clear_weather" | 测量时天气 |
| timestamp | string | 时间戳 | - | - | - | "2025-08-15T22:52:02.277641" | ISO 8601格式 |

#### detection_probability数组元素字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| sample_id | integer | 样本标识符 | - | ≥0 | - | 0 | 样本序号 |
| detection_probability | number | 探测概率 | - | 0-1 | - | 0.724 | 目标被探测到的概率 |
| distance_m | number | 目标距离 | m | >0 | - | 5017.6 | 目标到传感器距离 |
| base_probability | number | 基础探测概率 | - | 0-1 | - | 0.95 | 理论探测概率 |
| weather_factor | number | 天气影响因子 | - | 0-1 | - | 0.928 | 天气对探测的影响 |
| noise_factor | number | 噪声影响因子 | - | 0-1 | - | 0.821 | 噪声对探测的影响 |
| timestamp | string | 时间戳 | - | - | - | "2025-08-15T22:52:02.277641" | ISO 8601格式 |

#### statistics字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| total_samples | integer | 总样本数 | 个 | ≥0 | - | 3 | 生成的样本总数 |
| data_categories | integer | 数据类别数 | 个 | ≥0 | - | 4 | 数据类别总数 |
| sample_count_per_category | object | 各类别样本数 | - | - | - | - | 各类别的样本统计 |

### 光电干扰仿真数据结构（jammers数组元素）

#### device_info字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| device_id | integer | 设备标识符 | - | ≥0 | - | 0 | 设备在数组中的索引 |
| model | string | 设备型号 | - | - | - | "多目标_烟幕干扰1" | 输入配置的回显 |
| device_type | string | 设备类型 | - | - | optical_jammer | "optical_jammer" | 固定值 |
| jammer_type | string | 干扰器类型 | - | - | smoke_screen, laser_dazzler, infrared_decoy, chaff | "smoke_screen" | 自动识别的类型 |
| generation_timestamp | string | 数据生成时间戳 | - | - | - | "2025-08-15T22:51:40.857051" | ISO 8601格式 |

#### jamming_data字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| effectiveness | array | 干扰效果数据 | - | - | - | - | 干扰效果评估数据 |
| power_consumption | array | 功耗数据 | - | - | - | - | 设备功耗统计数据 |
| coverage | array | 覆盖范围数据 | - | - | - | - | 干扰覆盖范围数据 |
| duration | array | 持续时间数据 | - | - | - | - | 干扰持续时间数据 |

#### effectiveness数组元素字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| sample_id | integer | 样本标识符 | - | ≥0 | - | 0 | 样本序号 |
| target_distance_m | number | 目标距离 | m | >0 | - | 1467.6 | 干扰目标距离 |
| base_effectiveness | number | 基础干扰效果 | - | 0-1 | - | 0.0 | 理论干扰效果 |
| weather_factor | number | 天气影响因子 | - | 0-1 | - | 0.98 | 天气对干扰的影响 |
| atmospheric_factor | number | 大气影响因子 | - | 0-1 | - | 0.996 | 大气对干扰的影响 |
| target_vulnerability | number | 目标脆弱性 | - | 0-1 | - | 0.913 | 目标对干扰的敏感性 |
| final_effectiveness | number | 最终干扰效果 | - | 0-1 | - | 0.0 | 综合干扰效果 |
| jammer_type | string | 干扰器类型 | - | - | - | "smoke_screen" | 干扰器类型标识 |
| timestamp | string | 时间戳 | - | - | - | "2025-08-15T22:51:40.856551" | ISO 8601格式 |

#### power_consumption数组元素字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| sample_id | integer | 样本标识符 | - | ≥0 | - | 0 | 样本序号 |
| base_power_w | number | 基础功率 | W | >0 | - | 1000 | 设备额定功率 |
| mode_factor | number | 模式影响因子 | - | 0-1 | - | 0.686 | 工作模式对功率的影响 |
| temp_factor | number | 温度影响因子 | - | >0 | - | 1.006 | 温度对功率的影响 |
| efficiency | number | 设备效率 | - | 0-1 | - | 0.877 | 设备工作效率 |
| actual_power_w | number | 实际功率 | W | >0 | - | 690.5 | 实际消耗功率 |
| total_power_w | number | 总功率 | W | >0 | - | 787.8 | 包含损耗的总功率 |
| work_mode | string | 工作模式 | - | - | - | "area_denial" | 当前工作模式 |
| timestamp | string | 时间戳 | - | - | - | "2025-08-15T22:51:40.856551" | ISO 8601格式 |

#### coverage数组元素字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| sample_id | integer | 样本标识符 | - | ≥0 | - | 0 | 样本序号 |
| base_range_m | number | 基础覆盖距离 | m | >0 | - | 4000 | 理论覆盖距离 |
| power_factor | number | 功率影响因子 | - | >0 | - | 1.0 | 功率对覆盖的影响 |
| weather_factor | number | 天气影响因子 | - | 0-1 | - | 0.993 | 天气对覆盖的影响 |
| terrain_factor | number | 地形影响因子 | - | >0 | - | 1.061 | 地形对覆盖的影响 |
| actual_range_m | number | 实际覆盖距离 | m | >0 | - | 4213.8 | 实际覆盖距离 |
| coverage_angle_deg | number | 覆盖角度 | 度 | 0-360 | - | 161.1 | 覆盖扇形角度 |
| jammer_type | string | 干扰器类型 | - | - | - | "smoke_screen" | 干扰器类型标识 |
| timestamp | string | 时间戳 | - | - | - | "2025-08-15T22:51:40.857051" | ISO 8601格式 |

#### duration数组元素字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| sample_id | integer | 样本标识符 | - | ≥0 | - | 0 | 样本序号 |
| base_duration_s | number | 基础持续时间 | s | >0 | - | 300 | 理论持续时间 |
| wind_factor | number | 风力影响因子 | - | 0-1 | - | 0.5 | 风力对持续时间的影响 |
| power_factor | number | 功率影响因子 | - | >0 | - | 0.858 | 功率对持续时间的影响 |
| actual_duration_s | number | 实际持续时间 | s | >0 | - | 128.6 | 实际持续时间 |
| wind_speed_ms | number | 风速 | m/s | ≥0 | - | 5.0 | 当前风速 |
| jammer_type | string | 干扰器类型 | - | - | - | "smoke_screen" | 干扰器类型标识 |
| timestamp | string | 时间戳 | - | - | - | "2025-08-15T22:51:40.857051" | ISO 8601格式 |

#### performance_summary字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| max_power_w | number | 最大功率 | W | >0 | - | 1000 | 设备最大功率 |
| max_range_m | number | 最大覆盖距离 | m | >0 | - | 4000 | 最大覆盖距离 |
| frequency_hz | number | 工作频率 | Hz | >0 | - | 1000 | 设备工作频率 |
| effectiveness_range | array | 效果范围 | - | [min, max] | - | [0.1, 0.98] | 干扰效果范围 |

#### environmental_factors字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| weather_condition | string | 天气条件 | - | - | - | "clear_weather" | 当前天气条件 |
| temperature_k | number | 环境温度 | K | >0 | - | 291.15 | 环境温度 |
| humidity | number | 相对湿度 | - | 0-1 | - | 0.6 | 相对湿度 |
| wind_speed_ms | number | 风速 | m/s | ≥0 | - | 5.0 | 当前风速 |

### 光电侦察仿真数据结构（recons数组元素）

#### device_info字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| device_id | integer | 设备标识符 | - | ≥0 | - | 0 | 设备在数组中的索引 |
| model | string | 设备型号 | - | - | - | "多目标_侦察系统1" | 输入配置的回显 |
| device_type | string | 设备类型 | - | - | optical_recon | "optical_recon" | 固定值 |
| recon_type | string | 侦察器类型 | - | - | infrared_detector, laser_warning, electro_optical | "infrared_detector" | 自动识别的类型 |
| detection_mode | string | 检测模式 | - | - | - | "infrared_warning" | 输入配置的回显 |
| work_mode | string | 工作模式 | - | - | - | "multi_target_tracking" | 输入配置的回显 |
| generation_timestamp | string | 数据生成时间戳 | - | - | - | "2025-08-15T22:51:40.859550" | ISO 8601格式 |

#### reconnaissance_data字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| initial_screening | array | 初筛检测数据 | - | - | - | - | 初步目标检测数据 |
| feature_extraction | array | 特征提取数据 | - | - | - | - | 目标特征提取数据 |
| target_tracking | array | 目标跟踪数据 | - | - | - | - | 目标跟踪性能数据 |
| recognition_accuracy | array | 识别准确率数据 | - | - | - | - | 目标识别性能数据 |
| detection_range | array | 探测距离数据 | - | - | - | - | 探测性能数据 |
| discovery_probability | array | 发现概率数据 | - | - | - | - | 目标发现概率数据 |

#### initial_screening数组元素字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| sample_id | integer | 样本标识符 | - | ≥0 | - | 0 | 样本序号 |
| target_present | boolean | 目标是否存在 | - | - | true, false | false | 真实目标存在状态 |
| signal_strength | number | 信号强度 | - | 0-1 | - | 0.268 | 检测到的信号强度 |
| noise_level | number | 噪声水平 | - | 0-1 | - | 0.099 | 背景噪声水平 |
| snr_db | number | 信噪比 | dB | - | - | 8.6 | 信号噪声比 |
| detected | boolean | 是否检测到 | - | - | true, false | true | 系统检测结果 |
| result_type | string | 检测结果类型 | - | - | hit, miss, false_alarm, correct_rejection | "false_alarm" | 检测结果分类 |
| recon_type | string | 侦察器类型 | - | - | - | "infrared_detector" | 侦察器类型标识 |
| timestamp | string | 时间戳 | - | - | - | "2025-08-15T22:51:40.858551" | ISO 8601格式 |

#### feature_extraction数组元素字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| sample_id | integer | 样本标识符 | - | ≥0 | - | 0 | 样本序号 |
| extracted_features | array | 提取的特征类型 | - | - | spectral, spatial, temporal, polarization | ["spectral", "temporal"] | 成功提取的特征 |
| feature_quality | object | 特征质量评估 | - | - | - | - | 各特征的质量评分 |
| overall_confidence | number | 总体置信度 | - | 0-1 | - | 0.804 | 特征提取总体置信度 |
| processing_time_s | number | 处理时间 | s | >0 | - | 1.11 | 特征提取耗时 |
| recon_type | string | 侦察器类型 | - | - | - | "infrared_detector" | 侦察器类型标识 |
| timestamp | string | 时间戳 | - | - | - | "2025-08-15T22:51:40.859050" | ISO 8601格式 |

#### target_tracking数组元素字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| sample_id | integer | 样本标识符 | - | ≥0 | - | 0 | 样本序号 |
| target_speed_ms | number | 目标速度 | m/s | ≥0 | - | 17.2 | 目标运动速度 |
| target_direction_deg | number | 目标方向 | 度 | 0-360 | - | 202.9 | 目标运动方向 |
| tracking_accuracy | number | 跟踪精度 | - | 0-1 | - | 0.854 | 跟踪精度评估 |
| position_error_m | number | 位置误差 | m | ≥0 | - | 6.1 | 位置估计误差 |
| velocity_error_ms | number | 速度误差 | m/s | ≥0 | - | 1.46 | 速度估计误差 |
| track_state | string | 跟踪状态 | - | - | acquiring, tracking, coasting, lost | "tracking" | 当前跟踪状态 |
| track_duration_s | number | 跟踪持续时间 | s | ≥0 | - | 131.6 | 连续跟踪时间 |
| speed_factor | number | 速度影响因子 | - | 0-1 | - | 0.993 | 速度对跟踪的影响 |
| distance_factor | number | 距离影响因子 | - | 0-1 | - | 0.956 | 距离对跟踪的影响 |
| timestamp | string | 时间戳 | - | - | - | "2025-08-15T22:51:40.859050" | ISO 8601格式 |

#### recognition_accuracy数组元素字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| sample_id | integer | 样本标识符 | - | ≥0 | - | 0 | 样本序号 |
| true_type | string | 真实目标类型 | - | - | aircraft, missile, vehicle, ship, unknown | "unknown" | 目标真实类型 |
| recognized_type | string | 识别目标类型 | - | - | aircraft, missile, vehicle, ship, unknown | "missile" | 系统识别类型 |
| correct_recognition | boolean | 识别是否正确 | - | - | true, false | false | 识别结果正确性 |
| confidence | number | 识别置信度 | - | 0-1 | - | 0.65 | 系统识别置信度 |
| recognition_accuracy | number | 识别准确率 | - | 0-1 | - | 0.444 | 综合识别准确率 |
| distance_m | number | 目标距离 | m | >0 | - | 18100.2 | 目标到传感器距离 |
| distance_factor | number | 距离影响因子 | - | 0-1 | - | 0.749 | 距离对识别的影响 |
| weather_factor | number | 天气影响因子 | - | 0-1 | - | 0.913 | 天气对识别的影响 |
| target_size_factor | number | 目标尺寸因子 | - | >0 | - | 0.634 | 目标尺寸对识别的影响 |
| timestamp | string | 时间戳 | - | - | - | "2025-08-15T22:51:40.859050" | ISO 8601格式 |

#### detection_range数组元素字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| sample_id | integer | 样本标识符 | - | ≥0 | - | 0 | 样本序号 |
| base_range_m | number | 基础探测距离 | m | >0 | - | 35000 | 理论探测距离 |
| weather_factor | number | 天气影响因子 | - | 0-1 | - | 0.922 | 天气对探测的影响 |
| target_signature | number | 目标特征强度 | - | >0 | - | 1.046 | 目标信号特征强度 |
| sensor_performance | number | 传感器性能 | - | 0-1 | - | 0.943 | 传感器当前性能 |
| actual_range_m | number | 实际探测距离 | m | >0 | - | 31856.2 | 实际探测距离 |
| weather_condition | string | 天气条件 | - | - | - | "clear_weather" | 当前天气条件 |
| recon_type | string | 侦察器类型 | - | - | - | "infrared_detector" | 侦察器类型标识 |
| timestamp | string | 时间戳 | - | - | - | "2025-08-15T22:51:40.859550" | ISO 8601格式 |

#### discovery_probability数组元素字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| sample_id | integer | 样本标识符 | - | ≥0 | - | 0 | 样本序号 |
| distance_m | number | 目标距离 | m | >0 | - | 22832.0 | 目标到传感器距离 |
| base_probability | number | 基础发现概率 | - | 0-1 | - | 0.636 | 理论发现概率 |
| weather_factor | number | 天气影响因子 | - | 0-1 | - | 0.897 | 天气对发现的影响 |
| atmospheric_factor | number | 大气影响因子 | - | 0-1 | - | 0.913 | 大气对发现的影响 |
| target_visibility | number | 目标可见性 | - | >0 | - | 0.855 | 目标可见性评估 |
| sensor_condition | number | 传感器状态 | - | 0-1 | - | 0.965 | 传感器工作状态 |
| discovery_probability | number | 发现概率 | - | 0-1 | - | 0.43 | 综合发现概率 |
| recon_type | string | 侦察器类型 | - | - | - | "infrared_detector" | 侦察器类型标识 |
| timestamp | string | 时间戳 | - | - | - | "2025-08-15T22:51:40.859550" | ISO 8601格式 |

#### performance_summary字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| max_detection_range_m | number | 最大探测距离 | m | >0 | - | 35000 | 设备最大探测距离 |
| resolution_mrad | number | 分辨率 | mrad | >0 | - | 0.02 | 设备角分辨率 |
| spectral_coverage_m | array | 光谱覆盖范围 | m | [min, max] | - | [3e-06, 1.2e-05] | 工作波段范围 |
| detection_threshold | number | 检测阈值 | - | 0-1 | - | 0.7 | 目标检测阈值 |
| tracking_accuracy | number | 跟踪精度 | - | 0-1 | - | 0.9 | 目标跟踪精度 |
| false_alarm_rate | number | 虚警率 | - | 0-1 | - | 0.05 | 系统虚警率 |

#### sensor_specifications字段规范

| 字段 | 类型 | 说明 | 单位 | 范围 | 可选值 | 参考值 | 备注 |
|------|------|------|------|------|--------|--------|------|
| detector_type | string | 探测器类型 | - | - | mercury_cadmium_telluride, indium_gallium_arsenide, silicon | "mercury_cadmium_telluride" | 探测器材料类型 |
| image_resolution | array | 图像分辨率 | 像素 | [width, height] | - | [640, 480] | 成像分辨率 |
| spectral_range | array | 光谱范围 | m | [min, max] | - | [1e-06, 1.2e-05] | 探测器光谱范围 |
| quantum_efficiency | number | 量子效率 | - | 0-1 | - | 0.7 | 探测器量子效率 |

## 输入输出示例

### 输入示例

```json
{
  "simulation": {
    "scenario_name": "多目标光电对抗场景",
    "duration": 10.0,
    "time_step": 0.1,
    "data_count": 3,
    "output_types": ["static_images", "dynamic_images", "parameters"],
    "environment": {
      "weather_condition": "clear_weather",
      "temperature": 291.15,
      "humidity": 0.6,
      "pressure": 101325,
      "wind_speed": 5.0,
      "visibility": 25000
    }
  },
  "system": {
    "max_threads": 8,
    "image_resolution": [640, 480],
    "video_fps": 30,
    "random_seed": 1234
  },
  "optical_targets": [
    {
      "model": "多目标_红外目标A",
      "position": {
        "latitude": 39.9042,
        "longitude": 116.4074,
        "altitude": 1500.0
      },
      "observation_direction": {
        "azimuth": 45.0,
        "elevation": 10.0
      },
      "performance_params": {
        "detection_range": 20000,
        "resolution": 0.06,
        "field_of_view": 15.0
      },
      "work_mode": "coordinated_search",
      "temperature": {
        "engine": 520.0,
        "body": 305.0,
        "background": 291.15
      }
    }
  ],
  "optical_jammers": [
    {
      "model": "多目标_烟幕干扰1",
      "position": {
        "latitude": 39.9020,
        "longitude": 116.4020,
        "altitude": 500.0
      },
      "jamming_direction": {
        "azimuth": 60.0,
        "elevation": 0.0
      },
      "performance_params": {
        "coverage_range": 4000,
        "duration": 300,
        "coverage_radius": 150
      },
      "work_mode": "area_denial",
      "jamming_strategy": "coordinated_obscuration"
    }
  ],
  "optical_recons": [
    {
      "model": "多目标_侦察系统1",
      "position": {
        "latitude": 39.9200,
        "longitude": 116.4200,
        "altitude": 2500.0
      },
      "performance_params": {
        "detection_range": 35000,
        "resolution": 0.02,
        "spectral_coverage": [3e-6, 12e-6],
        "sensitivity": 0.96,
        "field_of_view": 30.0,
        "multi_target_capacity": 15
      },
      "work_mode": "multi_target_tracking",
      "detection_mode": "infrared_warning"
    }
  ]
}
```

### 输出示例（简化版）

```json
{
  "success": true,
  "session_info": {
    "start_time": "2025-08-15T22:51:40.844053",
    "end_time": "2025-08-15T22:52:02.333639",
    "duration": 21.489586,
    "session_id": "session_20250815_225140_844",
    "output_directory": "simulation_results/session_20250815_225140_844"
  },
  "simulation_config": {
    "scenario_name": "多目标光电对抗场景",
    "duration": 10.0,
    "time_step": 0.1,
    "data_count": 3,
    "output_types": ["static_images", "dynamic_images", "parameters"],
    "device_count": {
      "optical_targets": 1,
      "optical_jammers": 1,
      "optical_recons": 1
    }
  },
  "simulation_results": {
    "images": [
      "simulation_results/session_20250815_225140_844/images/target_0_static_0000.png",
      "simulation_results/session_20250815_225140_844/images/target_0_static_0001.png",
      "simulation_results/session_20250815_225140_844/images/target_0_static_0002.png"
    ],
    "videos": [
      "simulation_results/session_20250815_225140_844/videos/target_0_dynamic.mp4"
    ],
    "data": [
      "simulation_results/session_20250815_225140_844/data/target_0_parameters.json",
      "simulation_results/session_20250815_225140_844/data/jammer_0_comprehensive.json",
      "simulation_results/session_20250815_225140_844/data/recon_0_comprehensive.json"
    ],
    "summary": [
      "simulation_results/session_20250815_225140_844/data/simulation_summary.json"
    ],
    "simulation_data": {
      "targets": [
        {
          "device_info": {
            "device_id": 0,
            "model": "多目标_红外目标A",
            "device_type": "optical_target",
            "generation_timestamp": "2025-08-15T22:52:02.277641"
          },
          "parameter_data": {
            "deviation_range": [
              {
                "sample_id": 0,
                "azimuth_deviation_mrad": 47.066,
                "elevation_deviation_mrad": 77.284,
                "total_deviation_mrad": 90.488,
                "weather_condition": "clear_weather",
                "timestamp": "2025-08-15T22:52:02.277641"
              }
            ],
            "recognition_accuracy": [
              {
                "sample_id": 0,
                "recognition_accuracy": 0.499,
                "distance_m": 15076.5,
                "weather_factor": 0.913,
                "distance_factor": 0.704,
                "timestamp": "2025-08-15T22:52:02.277641"
              }
            ]
          }
        }
      ],
      "jammers": [
        {
          "device_info": {
            "device_id": 0,
            "model": "多目标_烟幕干扰1",
            "device_type": "optical_jammer",
            "jammer_type": "smoke_screen"
          },
          "jamming_data": {
            "effectiveness": [
              {
                "sample_id": 0,
                "target_distance_m": 1467.6,
                "base_effectiveness": 0.0,
                "final_effectiveness": 0.0,
                "jammer_type": "smoke_screen"
              }
            ]
          }
        }
      ],
      "recons": [
        {
          "device_info": {
            "device_id": 0,
            "model": "多目标_侦察系统1",
            "device_type": "optical_recon",
            "recon_type": "infrared_detector"
          },
          "reconnaissance_data": {
            "initial_screening": [
              {
                "sample_id": 0,
                "target_present": false,
                "signal_strength": 0.268,
                "snr_db": 8.6,
                "detected": true,
                "result_type": "false_alarm"
              }
            ]
          }
        }
      ]
    }
  },
  "performance_metrics": {
    "processing_speed": 0.14,
    "files_per_second": 1.12,
    "thread_count": 8,
    "thread_utilization": 0.8
  },
  "server_info": {
    "ipv4_address": "*************",
    "port": 8587,
    "base_url": "http://*************:8587",
    "static_files_url": "http://*************:8587/simulation_results"
  }
}
```

## 数据验证规则

### 输入数据验证

1. **必需字段验证**：
   - `simulation`、`system`、`optical_targets`为必需字段
   - 至少需要配置一种设备（targets、jammers或recons）
   - 每个设备必须包含`model`、`position`、`performance_params`、`work_mode`

2. **数值范围验证**：
   - `data_count`：1-5000
   - `duration`、`time_step`：必须大于0
   - `latitude`：-90到90度
   - `longitude`：-180到180度
   - `altitude`：必须大于等于0
   - 角度值：方位角0-360度，俯仰角-90到90度

3. **类型验证**：
   - 所有数值字段必须是数字类型
   - 布尔字段必须是boolean类型
   - 数组字段必须是array类型
   - 对象字段必须是object类型

### 输出数据特点

1. **路径编码**：所有文件路径都经过URL编码处理，支持中文文件名
2. **时间戳格式**：统一使用ISO 8601格式（YYYY-MM-DDTHH:mm:ss.ffffff）
3. **数据完整性**：每个设备都会生成完整的仿真数据，包含多个性能指标
4. **统计信息**：提供详细的数据生成统计和性能指标
